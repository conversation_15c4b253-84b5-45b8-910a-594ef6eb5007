import * as THREE from "three";
import { G<PERSON> } from "dat.gui";
import { EARTH_RADIUS } from "../../../../constants/index.js";

/**
 * 星球环系统 - 基于着色器的连续圆环
 *
 * 创建类似土星环的连续圆环效果，使用着色器实现平滑的渐变和透明度。
 *
 * 特性：
 * 1. 连续的圆环几何体，不是粒子系统
 * 2. 自定义着色器实现渐变和透明度效果
 * 3. 多层环结构，每层有不同的颜色和透明度
 * 4. 平滑的旋转动画
 * 5. 可配置的环参数
 *
 * @class PlanetRings
 */
class PlanetRings {
  constructor(scene, options = {}) {
    this.scene = scene;

    // 配置参数 - 每个环的独立配置
    this.config = {
      // 全局参数
      segments: options.segments || 128,
      enableGUI: options.enableGUI !== false,
      visible: options.visible !== false,

      // 每个环的独立配置
      rings: options.rings || [
        {
          // 第一个环配置
          innerRadius: 25.2,
          outerRadius: 27.1,
          color: 0x97caff,
          opacity: 0.18,
          rotationSpeed: 0.002,
          enableRotation: true,
          crossAngle: -22, // 交叉角度（度）
          tiltAngle: 0, // 倾斜角度（度）
          yRotation: 149, // Y轴旋转（度）
          scale: 1.0, // 缩放比例
          // 位置偏移
          positionX: -4.1, // X轴位置偏移
          positionY: 5.5, // Y轴位置偏移
          positionZ: 0, // Z轴位置偏移
        },
        {
          // 第二个环配置
          innerRadius: 31.7,
          outerRadius: 33.4,
          color: 0x22defd,
          opacity: 0.1,
          rotationSpeed: -0.0015,
          enableRotation: true,
          crossAngle: 42, // 交叉角度（度）
          tiltAngle: -17, // 倾斜角度（度）
          yRotation: 172, // Y轴旋转（度）
          scale: 1.0, // 缩放比例
          // 位置偏移
          positionX: 0, // X轴位置偏移
          positionY: 6.7, // Y轴位置偏移
          positionZ: 0, // Z轴位置偏移
        },
      ],
    };

    // Three.js 对象
    this.ringMeshes = [];
    this.ringGroup = new THREE.Group();
    this.ringGroup.name = "PlanetRingsGroup";

    // 动画相关
    this.scaleAnimationId = null;

    // GUI相关
    this.gui = null;
    this.folder = null;

    this.init();
  }

  init() {
    this.createRings();
    this.addToScene();

    // 初始化时将圆环缩放设置为0（隐藏状态）
    this.setInitialScale(0);

    // 初始化GUI控制面板
    if (this.config.enableGUI) {
      // this.initGUI();
    }
  }

  /**
   * 创建多层圆环
   */
  createRings() {
    // 清理现有的环
    this.ringMeshes.forEach((mesh) => {
      this.ringGroup.remove(mesh);
      if (mesh.geometry) mesh.geometry.dispose();
      if (mesh.material) mesh.material.dispose();
    });
    this.ringMeshes = [];

    // 根据配置创建每个环
    this.config.rings.forEach((ringConfig, index) => {
      // 确保每个环都有缩放属性
      if (typeof ringConfig.scale === "undefined") {
        ringConfig.scale = 1.0;
      }

      // 确保每个环都有位置属性
      if (typeof ringConfig.positionX === "undefined") {
        ringConfig.positionX = 0;
      }
      if (typeof ringConfig.positionY === "undefined") {
        ringConfig.positionY = 0;
      }
      if (typeof ringConfig.positionZ === "undefined") {
        ringConfig.positionZ = 0;
      }

      const color = new THREE.Color(ringConfig.color);
      const ringMesh = this.createSingleRing(ringConfig.innerRadius, ringConfig.outerRadius, color, index, ringConfig);
      this.ringMeshes.push(ringMesh);
      this.ringGroup.add(ringMesh);
    });
  }

  /**
   * 创建单个圆环
   */
  createSingleRing(innerRadius, outerRadius, color, ringIndex, ringConfig) {
    // 创建圆环几何体
    const geometry = new THREE.RingGeometry(innerRadius, outerRadius, this.config.segments);

    // 创建自定义着色器材质
    const material = this.createRingMaterial(color, innerRadius, outerRadius, ringIndex, ringConfig);

    // 创建网格
    const mesh = new THREE.Mesh(geometry, material);
    mesh.name = `PlanetRing_${ringIndex}`;

    // 存储环的配置引用
    mesh.userData.ringConfig = ringConfig;
    mesh.userData.ringIndex = ringIndex;

    // 设置环的方向和角度
    this.setupRingOrientation(mesh, ringIndex, ringConfig);

    return mesh;
  }

  /**
   * 设置环的方向和倾斜角度，创建交叉效果
   */
  setupRingOrientation(mesh, ringIndex, ringConfig) {
    // 基础旋转：让环从垂直变为接近水平
    mesh.rotation.x = -Math.PI / 2;

    // 使用环的独立配置参数
    const crossAngleRad = (ringConfig.crossAngle * Math.PI) / 180; // 交叉角度
    const tiltAngleRad = (ringConfig.tiltAngle * Math.PI) / 180; // 倾斜角度
    const yRotationRad = (ringConfig.yRotation * Math.PI) / 180; // Y轴旋转

    // 应用配置的角度
    mesh.rotation.x += tiltAngleRad; // 倾斜角度
    mesh.rotation.y = yRotationRad; // Y轴旋转
    mesh.rotation.z = crossAngleRad; // 交叉角度

    // 应用缩放
    const scale = ringConfig.scale || 1.0;
    mesh.scale.set(scale, scale, scale);

    // 应用位置偏移
    mesh.position.set(ringConfig.positionX || 0, ringConfig.positionY || 0, ringConfig.positionZ || 0);

    // 存储初始旋转，用于后续的旋转动画
    mesh.userData.initialRotation = {
      x: mesh.rotation.x,
      y: mesh.rotation.y,
      z: mesh.rotation.z,
    };
  }

  /**
   * 创建圆环材质
   */
  createRingMaterial(color, innerRadius, outerRadius, ringIndex, ringConfig) {
    // 创建高质量的着色器材质
    const vertexShader = `
      varying vec2 vUv;
      varying float vDistanceFromCenter;

      void main() {
        vUv = uv;
        vDistanceFromCenter = length(position.xy);
        gl_Position = projectionMatrix * modelViewMatrix * vec4(position, 1.0);
      }
    `;

    const fragmentShader = `
      uniform vec3 uColor;
      uniform float uOpacity;
      uniform float uTime;
      uniform float uInnerRadius;
      uniform float uOuterRadius;
      uniform float uRingIndex;

      varying vec2 vUv;
      varying float vDistanceFromCenter;

      // 噪声函数
      float random(vec2 st) {
        return fract(sin(dot(st.xy, vec2(12.9898, 78.233))) * 43758.5453123);
      }

      float noise(vec2 st) {
        vec2 i = floor(st);
        vec2 f = fract(st);

        float a = random(i);
        float b = random(i + vec2(1.0, 0.0));
        float c = random(i + vec2(0.0, 1.0));
        float d = random(i + vec2(1.0, 1.0));

        vec2 u = f * f * (3.0 - 2.0 * f);

        return mix(a, b, u.x) + (c - a) * u.y * (1.0 - u.x) + (d - b) * u.x * u.y;
      }

      void main() {
        // 计算角度（0到2π）
        float angle = atan(vUv.y - 0.5, vUv.x - 0.5) + 3.14159;

        // 计算径向进度
        float radialProgress = (vDistanceFromCenter - uInnerRadius) / (uOuterRadius - uInnerRadius);

        // 边缘渐变效果
        float edgeFade = 1.0 - pow(abs(radialProgress - 0.5) * 2.0, 1.5);

        // 径向条纹效果
        float radialStripes = sin(radialProgress * 25.0 + uTime * 2.0) * 0.2 + 0.8;

        // 角度变化 - 创建圆周方向的密度变化
        float angleVariation1 = sin(angle * 3.0 + uTime * 0.5 + uRingIndex) * 0.4 + 0.6;
        float angleVariation2 = sin(angle * 7.0 - uTime * 0.3 + uRingIndex * 2.0) * 0.3 + 0.7;
        float angleVariation3 = sin(angle * 12.0 + uTime * 0.8 + uRingIndex * 1.5) * 0.2 + 0.8;

        // 添加噪声纹理
        vec2 noiseCoord = vec2(angle * 2.0, radialProgress * 10.0);
        float noiseValue = noise(noiseCoord + uTime * 0.1) * 0.3 + 0.7;

        // 组合所有变化
        float angularDensity = angleVariation1 * angleVariation2 * angleVariation3 * noiseValue;

        // 最终透明度
        float finalOpacity = uOpacity * edgeFade * radialStripes * angularDensity;
        finalOpacity = clamp(finalOpacity, 0.0, 1.0);

        // 颜色也可以有轻微变化
        vec3 finalColor = uColor * (0.8 + 0.2 * angularDensity);

        gl_FragColor = vec4(finalColor, finalOpacity);
      }
    `;

    return new THREE.ShaderMaterial({
      uniforms: {
        uColor: { value: color },
        uOpacity: { value: ringConfig.opacity },
        uTime: { value: 0.0 },
        uInnerRadius: { value: innerRadius },
        uOuterRadius: { value: outerRadius },
        uRingIndex: { value: ringIndex },
      },
      vertexShader,
      fragmentShader,
      transparent: true,
      side: THREE.DoubleSide,
      depthWrite: false,
      blending: THREE.AdditiveBlending,
    });
  }

  /**
   * 添加到场景
   */
  addToScene() {
    if (this.scene) {
      this.scene.add(this.ringGroup);
      console.log("PlanetRings: Added to scene with", this.ringMeshes.length, "rings");
      console.log("PlanetRings: Ring group position:", this.ringGroup.position);
      console.log("PlanetRings: Ring group visible:", this.ringGroup.visible);
    }
  }

  /**
   * 更新动画
   */
  update() {
    const time = performance.now() * 0.001;

    this.ringMeshes.forEach((mesh, index) => {
      const ringConfig = mesh.userData.ringConfig;

      // 旋转动画 - 每个环使用自己的旋转配置
      if (ringConfig.enableRotation) {
        // 获取初始旋转
        const initialRotation = mesh.userData.initialRotation;

        // 计算当前的旋转增量
        if (!mesh.userData.currentRotation) {
          mesh.userData.currentRotation = 0;
        }
        mesh.userData.currentRotation += ringConfig.rotationSpeed;

        // 重置到初始旋转
        mesh.rotation.x = initialRotation.x;
        mesh.rotation.y = initialRotation.y;
        mesh.rotation.z = initialRotation.z;

        // 在Z轴上添加旋转（环在自己平面内的旋转）
        mesh.rotation.z += mesh.userData.currentRotation;
      }

      // 更新着色器时间
      if (mesh.material.uniforms && mesh.material.uniforms.uTime) {
        mesh.material.uniforms.uTime.value = time;
      }
    });
  }

  /**
   * 设置可见性
   */
  setVisible(visible) {
    this.ringGroup.visible = visible;
  }

  /**
   * 设置初始缩放
   */
  setInitialScale(scale) {
    this.ringMeshes.forEach((mesh) => {
      mesh.scale.set(scale, scale, scale);
    });
  }

  /**
   * 缩放动画到目标大小（支持每个圆环不同的动画时长）
   * @param {number} targetScale - 目标缩放值（默认1.0）
   * @param {number|Array} duration - 动画持续时间（毫秒），可以是单个值或每个圆环的数组
   * @param {Function} onComplete - 动画完成回调
   */
  animateScaleTo(targetScale = 1.0, duration = 1000, onComplete = null) {
    // 停止之前的缩放动画
    if (this.scaleAnimationId) {
      cancelAnimationFrame(this.scaleAnimationId);
    }

    const startTime = performance.now();
    const startScales = this.ringMeshes.map((mesh) => mesh.scale.x); // 假设xyz缩放相同

    // 处理动画时长参数 - 支持数组或单个值
    let durations;
    if (Array.isArray(duration)) {
      durations = duration;
    } else {
      // 如果是单个值，为每个圆环设置不同的时长
      durations = this.ringMeshes.map((mesh, index) => {
        // 第一个圆环使用基础时长，第二个圆环时长增加50%
        return index === 0 ? duration : duration * 1.5;
      });
    }

    // 记录每个圆环的完成状态
    const ringCompleted = new Array(this.ringMeshes.length).fill(false);
    let completedCount = 0;

    const animate = (currentTime) => {
      const elapsed = currentTime - startTime;
      let allCompleted = true;

      // 更新每个环的缩放
      this.ringMeshes.forEach((mesh, index) => {
        if (ringCompleted[index]) return; // 已完成的圆环跳过

        const ringDuration = durations[index] || duration;
        const progress = Math.min(elapsed / ringDuration, 1);

        // 使用easeOutCubic缓动函数
        const easedProgress = 1 - Math.pow(1 - progress, 3);

        const startScale = startScales[index];
        const currentScale = startScale + (targetScale - startScale) * easedProgress;
        mesh.scale.set(currentScale, currentScale, currentScale);

        if (progress >= 1 && !ringCompleted[index]) {
          ringCompleted[index] = true;
          completedCount++;
          console.log(`圆环 ${index + 1} 缩放动画完成 (${ringDuration}ms)`);
        }

        if (!ringCompleted[index]) {
          allCompleted = false;
        }
      });

      if (allCompleted) {
        // 所有动画完成
        this.scaleAnimationId = null;
        if (onComplete) onComplete();
        console.log("所有圆环缩放动画完成");
        return;
      }

      this.scaleAnimationId = requestAnimationFrame(animate);
    };

    console.log(`开始圆环缩放动画 - 目标缩放: ${targetScale}`);
    console.log(`动画时长: 圆环1=${durations[0]}ms, 圆环2=${durations[1] || durations[0]}ms`);
    this.scaleAnimationId = requestAnimationFrame(animate);
  }

  /**
   * 停止缩放动画
   */
  stopScaleAnimation() {
    if (this.scaleAnimationId) {
      cancelAnimationFrame(this.scaleAnimationId);
      this.scaleAnimationId = null;
    }
  }

  /**
   * 使用自定义参数为每个圆环设置不同的动画
   * @param {Array} ringAnimations - 每个圆环的动画配置数组
   * @param {Function} onComplete - 所有动画完成回调
   *
   * 示例:
   * animateRingsWithCustomTiming([
   *   { targetScale: 1.0, duration: 1000, delay: 0 },
   *   { targetScale: 1.0, duration: 1500, delay: 200 }
   * ])
   */
  animateRingsWithCustomTiming(ringAnimations, onComplete = null) {
    // 停止之前的缩放动画
    if (this.scaleAnimationId) {
      cancelAnimationFrame(this.scaleAnimationId);
    }

    const startTime = performance.now();
    const startScales = this.ringMeshes.map((mesh) => mesh.scale.x);

    // 为每个圆环设置默认参数
    const animations = this.ringMeshes.map((mesh, index) => {
      const defaultAnimation = {
        targetScale: 1.0,
        duration: 1000,
        delay: 0,
      };

      return ringAnimations[index] ? { ...defaultAnimation, ...ringAnimations[index] } : defaultAnimation;
    });

    // 记录每个圆环的完成状态
    const ringCompleted = new Array(this.ringMeshes.length).fill(false);

    const animate = (currentTime) => {
      const elapsed = currentTime - startTime;
      let allCompleted = true;

      // 更新每个环的缩放
      this.ringMeshes.forEach((mesh, index) => {
        if (ringCompleted[index]) return; // 已完成的圆环跳过

        const animation = animations[index];
        const adjustedElapsed = elapsed - animation.delay;

        // 如果还在延迟期间，跳过这个圆环
        if (adjustedElapsed < 0) {
          allCompleted = false;
          return;
        }

        const progress = Math.min(adjustedElapsed / animation.duration, 1);

        // 使用easeOutCubic缓动函数
        const easedProgress = 1 - Math.pow(1 - progress, 3);

        const startScale = startScales[index];
        const currentScale = startScale + (animation.targetScale - startScale) * easedProgress;
        mesh.scale.set(currentScale, currentScale, currentScale);

        if (progress >= 1 && !ringCompleted[index]) {
          ringCompleted[index] = true;
          console.log(`圆环 ${index + 1} 缩放动画完成 (时长:${animation.duration}ms, 延迟:${animation.delay}ms)`);
        }

        if (!ringCompleted[index]) {
          allCompleted = false;
        }
      });

      if (allCompleted) {
        // 所有动画完成
        this.scaleAnimationId = null;
        if (onComplete) onComplete();
        console.log("所有圆环自定义缩放动画完成");
        return;
      }

      this.scaleAnimationId = requestAnimationFrame(animate);
    };

    console.log("开始圆环自定义缩放动画:");
    animations.forEach((anim, index) => {
      console.log(`  圆环${index + 1}: 目标缩放=${anim.targetScale}, 时长=${anim.duration}ms, 延迟=${anim.delay}ms`);
    });

    this.scaleAnimationId = requestAnimationFrame(animate);
  }

  /**
   * 更新环的颜色
   */
  updateRingColors(colors) {
    // 更新每个环配置中的颜色
    colors.forEach((color, index) => {
      if (index < this.config.rings.length) {
        this.config.rings[index].color = color;
      }
    });

    this.ringMeshes.forEach((mesh, index) => {
      const colorValue = colors[index % colors.length];
      const color = new THREE.Color(colorValue);
      if (mesh.material.uniforms && mesh.material.uniforms.uColor) {
        mesh.material.uniforms.uColor.value = color;
      } else {
        mesh.material.color = color;
      }
    });
  }

  /**
   * 更新透明度
   */
  setOpacity(opacity) {
    // 更新所有环的透明度
    this.config.rings.forEach((ring, index) => {
      ring.opacity = opacity;
      this.updateSingleRingOpacity(index, opacity);
    });
  }

  /**
   * 设置旋转速度
   */
  setRotationSpeed(speed) {
    // 更新所有环的旋转速度
    this.config.rings.forEach((ring) => {
      ring.rotationSpeed = speed;
    });
  }

  /**
   * 初始化GUI控制面板
   */
  initGUI() {
    try {
      // 获取或创建全局GUI实例
      if (window.gui) {
        this.gui = window.gui;
      } else {
        this.gui = new GUI();
        window.gui = this.gui;
      }

      // 创建星球环控制文件夹
      this.folder = this.gui.addFolder("🪐 星球环 (Planet Rings)");
      this.folder.open();

      this.setupGUIControls();
    } catch (error) {
      console.warn("PlanetRings GUI setup warning:", error);
    }
  }

  /**
   * 设置GUI控制项
   */
  setupGUIControls() {
    if (!this.folder) return;

    // 全局控制
    const globalFolder = this.folder.addFolder("全局设置");
    globalFolder.open();

    // 可见性控制
    globalFolder
      .add(this.config, "visible")
      .name("显示所有环")
      .onChange((value) => {
        this.setVisible(value);
      });

    // 为每个环创建独立的控制文件夹
    this.config.rings.forEach((ringConfig, index) => {
      const ringFolder = this.folder.addFolder(`🪐 环 ${index + 1}`);
      ringFolder.open();

      // 几何参数
      const geometryFolder = ringFolder.addFolder("几何参数");

      geometryFolder
        .add(ringConfig, "innerRadius", EARTH_RADIUS * 0.5, EARTH_RADIUS * 4, 0.1)
        .name("内半径")
        .onChange(() => this.rebuildRings());

      geometryFolder
        .add(ringConfig, "outerRadius", EARTH_RADIUS * 0.8, EARTH_RADIUS * 5, 0.1)
        .name("外半径")
        .onChange(() => this.rebuildRings());

      // 外观参数
      const appearanceFolder = ringFolder.addFolder("外观参数");

      const colorObj = { color: ringConfig.color };
      appearanceFolder
        .addColor(colorObj, "color")
        .name("颜色")
        .onChange((value) => {
          ringConfig.color = value;
          this.updateSingleRingColor(index, value);
        });

      appearanceFolder
        .add(ringConfig, "opacity", 0, 1, 0.01)
        .name("透明度")
        .onChange((value) => {
          this.updateSingleRingOpacity(index, value);
        });

      // 旋转参数
      const rotationFolder = ringFolder.addFolder("旋转参数");

      rotationFolder.add(ringConfig, "enableRotation").name("启用旋转");

      rotationFolder.add(ringConfig, "rotationSpeed", -0.01, 0.01, 0.0001).name("旋转速度");

      // 角度参数
      const angleFolder = ringFolder.addFolder("角度参数");

      angleFolder
        .add(ringConfig, "crossAngle", -90, 90, 1)
        .name("交叉角度(度)")
        .onChange(() => this.updateSingleRingOrientation(index));

      angleFolder
        .add(ringConfig, "tiltAngle", -90, 90, 1)
        .name("倾斜角度(度)")
        .onChange(() => this.updateSingleRingOrientation(index));

      angleFolder
        .add(ringConfig, "yRotation", 0, 360, 1)
        .name("Y轴旋转(度)")
        .onChange(() => this.updateSingleRingOrientation(index));

      // 缩放参数
      const scaleFolder = ringFolder.addFolder("缩放参数");

      scaleFolder
        .add(ringConfig, "scale", 0.1, 3.0, 0.01)
        .name("缩放比例")
        .onChange((value) => {
          this.updateSingleRingScale(index, value);
        });

      // 位置参数
      const positionFolder = ringFolder.addFolder("位置参数");

      positionFolder
        .add(ringConfig, "positionX", -EARTH_RADIUS * 2, EARTH_RADIUS * 2, 0.1)
        .name("X轴位置")
        .onChange((value) => {
          this.updateSingleRingPosition(index, "X", value);
        });

      positionFolder
        .add(ringConfig, "positionY", -EARTH_RADIUS * 2, EARTH_RADIUS * 2, 0.1)
        .name("Y轴位置")
        .onChange((value) => {
          this.updateSingleRingPosition(index, "Y", value);
        });

      positionFolder
        .add(ringConfig, "positionZ", -EARTH_RADIUS * 2, EARTH_RADIUS * 2, 0.1)
        .name("Z轴位置")
        .onChange((value) => {
          this.updateSingleRingPosition(index, "Z", value);
        });
    });

    // 预设和工具
    const toolsFolder = this.folder.addFolder("工具");

    const tools = {
      重建所有环: () => this.rebuildRings(),
      重置所有设置: () => this.resetToDefaults(),
      随机化角度: () => this.randomizeAngles(),
      添加环: () => this.addNewRing(),
      删除最后环: () => this.removeLastRing(),
    };

    Object.keys(tools).forEach((toolName) => {
      toolsFolder.add(tools, toolName).name(toolName);
    });
  }

  /**
   * 更新单个环的颜色
   */
  updateSingleRingColor(ringIndex, colorValue) {
    if (ringIndex < this.config.rings.length && ringIndex < this.ringMeshes.length) {
      const newColor = new THREE.Color(colorValue);
      this.config.rings[ringIndex].color = colorValue;

      const mesh = this.ringMeshes[ringIndex];
      if (mesh.material.uniforms && mesh.material.uniforms.uColor) {
        mesh.material.uniforms.uColor.value = newColor;
      } else {
        mesh.material.color = newColor;
      }
    }
  }

  /**
   * 应用颜色预设
   */
  applyColorPreset(colors) {
    this.updateRingColors(colors);
  }

  /**
   * 重建环系统
   */
  rebuildRings() {
    // 清理现有的环
    this.ringMeshes.forEach((mesh) => {
      this.ringGroup.remove(mesh);
      if (mesh.geometry) mesh.geometry.dispose();
      if (mesh.material) mesh.material.dispose();
    });
    this.ringMeshes = [];

    // 重新创建环
    this.createRings();
  }

  /**
   * 重置到默认设置
   */
  resetToDefaults() {
    // 重置全局设置
    this.config.visible = true;

    // 重置环配置为默认值
    this.config.rings = [
      {
        innerRadius: EARTH_RADIUS * 1.4,
        outerRadius: EARTH_RADIUS * 1.8,
        color: 0x4488ff,
        opacity: 0.4,
        rotationSpeed: 0.002,
        enableRotation: true,
        crossAngle: 0,
        tiltAngle: 0,
        yRotation: 0,
        scale: 1.0,
        positionX: 0,
        positionY: 0,
        positionZ: 0,
      },
      {
        innerRadius: EARTH_RADIUS * 2.0,
        outerRadius: EARTH_RADIUS * 2.4,
        color: 0xff8844,
        opacity: 0.4,
        rotationSpeed: -0.0015,
        enableRotation: true,
        crossAngle: 45,
        tiltAngle: 30,
        yRotation: 60,
        scale: 1.0,
        positionX: 0,
        positionY: 0,
        positionZ: 0,
      },
    ];

    this.setVisible(this.config.visible);
    this.rebuildRings();
  }

  /**
   * 更新所有环的方向
   */
  updateRingOrientations() {
    this.ringMeshes.forEach((mesh, index) => {
      this.setupRingOrientation(mesh, index);
    });
  }

  /**
   * 随机化角度设置
   */
  randomizeAngles() {
    this.config.rings.forEach((ringConfig) => {
      ringConfig.crossAngle = Math.random() * 180 - 90;
      ringConfig.tiltAngle = Math.random() * 180 - 90;
      ringConfig.yRotation = Math.random() * 360;
    });

    this.updateRingOrientations();
  }

  /**
   * 更新单个环的方向
   */
  updateSingleRingOrientation(ringIndex) {
    if (ringIndex < this.ringMeshes.length) {
      const mesh = this.ringMeshes[ringIndex];
      const ringConfig = this.config.rings[ringIndex];
      this.setupRingOrientation(mesh, ringIndex, ringConfig);
    }
  }

  /**
   * 更新单个环的透明度
   */
  updateSingleRingOpacity(ringIndex, opacity) {
    if (ringIndex < this.ringMeshes.length) {
      const mesh = this.ringMeshes[ringIndex];
      if (mesh.material.uniforms && mesh.material.uniforms.uOpacity) {
        mesh.material.uniforms.uOpacity.value = opacity;
      } else {
        mesh.material.opacity = opacity;
      }
    }
  }

  /**
   * 更新单个环的缩放
   */
  updateSingleRingScale(ringIndex, scale) {
    if (ringIndex < this.ringMeshes.length) {
      const mesh = this.ringMeshes[ringIndex];
      mesh.scale.set(scale, scale, scale);
    }
  }

  /**
   * 更新单个环的位置
   */
  updateSingleRingPosition(ringIndex, axis, value) {
    if (ringIndex < this.ringMeshes.length && ringIndex < this.config.rings.length) {
      const mesh = this.ringMeshes[ringIndex];
      const ringConfig = this.config.rings[ringIndex];

      // 更新配置
      ringConfig[`position${axis.toUpperCase()}`] = value;

      // 更新网格位置
      mesh.position[axis.toLowerCase()] = value;
    }
  }

  /**
   * 添加新环
   */
  addNewRing() {
    const newRingConfig = {
      innerRadius: EARTH_RADIUS * (2.5 + this.config.rings.length * 0.5),
      outerRadius: EARTH_RADIUS * (2.9 + this.config.rings.length * 0.5),
      color: Math.random() * 0xffffff,
      opacity: 0.4,
      rotationSpeed: (Math.random() - 0.5) * 0.004,
      enableRotation: true,
      crossAngle: Math.random() * 180 - 90,
      tiltAngle: Math.random() * 180 - 90,
      yRotation: Math.random() * 360,
      scale: 1.0, // 默认缩放比例
      // 位置偏移
      positionX: 0,
      positionY: 0,
      positionZ: 0,
    };

    this.config.rings.push(newRingConfig);
    this.rebuildRings();

    // 重新初始化GUI以包含新环的控制项
    if (this.folder && this.gui) {
      this.gui.removeFolder(this.folder);
      this.folder = this.gui.addFolder("🪐 星球环 (Planet Rings)");
      this.folder.open();
      this.setupGUIControls();
    }
  }

  /**
   * 删除最后一个环
   */
  removeLastRing() {
    if (this.config.rings.length > 1) {
      this.config.rings.pop();
      this.rebuildRings();

      // 重新初始化GUI
      if (this.folder && this.gui) {
        this.gui.removeFolder(this.folder);
        this.folder = this.gui.addFolder("🪐 星球环 (Planet Rings)");
        this.folder.open();
        this.setupGUIControls();
      }
    }
  }

  /**
   * 销毁资源
   */
  destroy() {
    // 停止缩放动画
    this.stopScaleAnimation();

    // 清理GUI
    if (this.folder && this.gui) {
      try {
        this.gui.removeFolder(this.folder);
      } catch (error) {
        console.warn("Error removing GUI folder:", error);
      }
    }

    // 从场景中移除
    if (this.scene && this.ringGroup) {
      this.scene.remove(this.ringGroup);
    }

    // 清理网格和材质
    this.ringMeshes.forEach((mesh) => {
      if (mesh.geometry) {
        mesh.geometry.dispose();
      }
      if (mesh.material) {
        mesh.material.dispose();
      }
    });

    // 清理数组
    this.ringMeshes = [];
    this.ringGroup = null;
    this.folder = null;
  }
}

export { PlanetRings };
