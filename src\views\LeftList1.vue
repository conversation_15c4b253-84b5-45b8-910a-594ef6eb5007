<template>
  <div class="LeftList1">
    <div class="list-container enhanced-container">
      <!-- 有数据时显示列表 -->
      <TransitionGroup v-if="sortedListData.length > 0" name="list" tag="div" class="list-items">
        <div
          v-for="(item, index) in sortedListData"
          :key="item.id"
          class="list-item enhanced-list-item"
          :class="[`enhanced-list-item-${index + 1}`, { 'data-updating': dataUpdateAnimating }]"
          :style="{ animationDelay: `${parseFloat(props.parentAnimationDelay) + 0.4 + index * 0.08}s` }"
        >
          <span class="item-number enhanced-number" :style="{ '--item-delay': `${parseFloat(props.parentAnimationDelay) + 0.4 + index * 0.08}s` }">No.{{ index + 1 }}</span>
          <span class="item-name enhanced-name" :style="{ '--item-delay': `${parseFloat(props.parentAnimationDelay) + 0.4 + index * 0.08}s` }">{{ item.name }}</span>
          <div class="progress-container enhanced-progress-container" :style="{ '--item-delay': `${parseFloat(props.parentAnimationDelay) + 0.4 + index * 0.08}s` }">
            <div class="progress-bar">
              <div class="progress-fill" :style="{ width: item.progress + '%' }"></div>
              <div class="progress-indicator" :style="{ left: item.progress + '%' }"></div>
            </div>
          </div>
          <span class="progress-value enhanced-value" :style="{ '--item-delay': `${parseFloat(props.parentAnimationDelay) + 0.4 + index * 0.08}s` }">{{ item.value }}</span>
        </div>
      </TransitionGroup>

      <!-- 无数据时显示暂无数据 -->
      <div v-else class="no-data enhanced-no-data">
        <div class="no-data-text">暂无数据</div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { computed, ref, watch } from "vue";
import { usedata } from "../store/data";
const dataStore = usedata();

// 接收父组件的动画延迟
const props = defineProps({
  parentAnimationDelay: {
    type: String,
    default: "0s",
  },
});

// 防抖数据，减少频繁更新导致的抖动
const debouncedListData = ref([]);

// 防抖定时器
let debounceTimer = null;

// 计算属性：从 dataStore 获取动态数据，只取前5条
const listData = computed(() => {
  const attackCountryTop = dataStore.data?.attackCountryTop;
  if (!attackCountryTop || !Array.isArray(attackCountryTop)) {
    // 如果数据还未加载，返回默认值
    return [];
  }

  // 只取前5条数据，并添加ID和进度计算
  const topFive = attackCountryTop.slice(0, 5);
  const maxValue = Math.max(...topFive.map((item) => item.attackNum || 0));

  return topFive.map((item, index) => ({
    id: index + 1,
    name: item.country || `国家${index + 1}`,
    value: item.attackNum || 0,
    progress: maxValue > 0 ? Math.round((item.attackNum / maxValue) * 100) : 0,
  }));
});

// 数据更新动画状态
const dataUpdateAnimating = ref(false);

// 触发数据更新动画
const triggerDataUpdateAnimation = () => {
  dataUpdateAnimating.value = true;
  setTimeout(() => {
    dataUpdateAnimating.value = false;
  }, 800); // 动画持续时间
};

// 监听数据变化并进行防抖处理
watch(
  listData,
  (newData) => {
    if (debounceTimer) {
      clearTimeout(debounceTimer);
    }

    debounceTimer = setTimeout(() => {
      // 检查数据是否真的发生了变化
      const hasChanged = JSON.stringify(newData) !== JSON.stringify(debouncedListData.value);

      if (hasChanged && debouncedListData.value.length > 0) {
        triggerDataUpdateAnimation();
      }

      debouncedListData.value = newData;
    }, 200); // 200ms 防抖延迟，减少频繁更新
  },
  { deep: true, immediate: true }
);

// 计算属性：按value值降序排序，使用防抖后的数据
const sortedListData = computed(() => {
  return [...debouncedListData.value].sort((a, b) => b.value - a.value);
});
</script>

<style lang="less" scoped>
.LeftList1 {
  width: 100%;
  height: 100%;
  padding: 20px;
  color: #fff;
  font-family: "Microsoft YaHei", sans-serif;
}

.list-container {
  width: 100%;
  height: 100%;
}

.list-items {
  width: 100%;
}

.list-item {
  display: flex;
  align-items: center;
  margin-bottom: 15px;
  gap: 15px;

  &:last-child {
    margin-bottom: 0;
  }
}

.item-number {
  color: white;
  font-weight: 500;
  font-size: 14px;
}

.item-name {
  color: #a0a0a0;
  font-weight: 400;
  font-size: 13px;
  flex-shrink: 0;
}

.progress-container {
  display: flex;
  align-items: center;
  flex: 1;
}

.progress-indicator {
  position: absolute;
  top: 50%;
  width: 18px;
  height: 18px;
  background-image: url("../../public/img/e.png");
  background-repeat: no-repeat;
  background-size: 100% 100%;
  border-radius: 50%;
  transform: translate(-50%, -50%);
  z-index: 2;
}

.progress-bar {
  flex: 1;
  height: 4px;
  background: rgba(0, 247, 255, 0.205);
  border-radius: 2px;
  position: relative;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, #707070 0%, #00bfff 100%);
  border-radius: 2px;
  transition: width 0.3s ease;
}

.progress-value {
  color: #ffffff;
  font-size: 16px;
  font-weight: 500;
  text-align: right;
  flex-shrink: 0;
}

// 增强的容器动画 - 优化为更平滑的淡入效果
.enhanced-container {
  animation: containerFadeIn 1000ms cubic-bezier(0.25, 0.46, 0.45, 0.94) forwards;
  animation-delay: 0.2s; /* 在父容器滑入后开始 */
  opacity: 0;
  transform: translateY(15px);
  will-change: transform, opacity;
}

@keyframes containerFadeIn {
  0% {
    opacity: 0;
    transform: translateY(15px) scale(0.98);
  }
  50% {
    opacity: 0.7;
    transform: translateY(5px) scale(0.99);
  }
  100% {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

// 增强的列表项动画 - 优化为更丝滑的效果
.enhanced-list-item {
  perspective: 1200px;
  transform-style: preserve-3d;
  animation: enhanced3DSlideIn 1000ms cubic-bezier(0.25, 0.46, 0.45, 0.94) forwards;
  transform: translateX(-80px) rotateY(-8deg) scale(0.85);
  opacity: 0;
  filter: drop-shadow(0 5px 15px rgba(50, 254, 252, 0.2));
  transition: all 0.5s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  will-change: transform, opacity, filter;
  backface-visibility: hidden;

  &:hover {
    transform: translateX(3px) rotateY(1deg) scale(1.01);
    filter: drop-shadow(0 8px 25px rgba(50, 254, 252, 0.4)) brightness(1.05);
    background: linear-gradient(90deg, rgba(50, 254, 252, 0.03) 0%, rgba(50, 254, 252, 0.08) 100%);
    border-radius: 6px;
  }
}

@keyframes enhanced3DSlideIn {
  0% {
    transform: translateX(-80px) rotateY(-8deg) scale(0.85);
    opacity: 0;
    filter: drop-shadow(0 2px 8px rgba(50, 254, 252, 0.1));
  }
  25% {
    transform: translateX(-30px) rotateY(-4deg) scale(0.92);
    opacity: 0.4;
    filter: drop-shadow(0 3px 10px rgba(50, 254, 252, 0.15));
  }
  50% {
    transform: translateX(-8px) rotateY(-1deg) scale(0.98);
    opacity: 0.7;
    filter: drop-shadow(0 4px 12px rgba(50, 254, 252, 0.25));
  }
  75% {
    transform: translateX(2px) rotateY(0.5deg) scale(1.01);
    opacity: 0.9;
    filter: drop-shadow(0 5px 14px rgba(50, 254, 252, 0.3));
  }
  100% {
    transform: translateX(0) rotateY(0deg) scale(1);
    opacity: 1;
    filter: drop-shadow(0 5px 15px rgba(50, 254, 252, 0.2));
  }
}

// 列表动画效果 - 优化TransitionGroup动画为更丝滑的效果
.list-enter-active,
.list-leave-active {
  transition: all 0.8s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.list-enter-from {
  opacity: 0;
  transform: translateX(-20px) scale(0.95);
}

.list-leave-to {
  opacity: 0;
  transform: translateX(20px) scale(0.95);
}

.list-move {
  transition: transform 0.8s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

// 增强的数字动画 - 优化为更平滑的效果
.enhanced-number {
  animation: numberGlow 800ms cubic-bezier(0.25, 0.46, 0.45, 0.94) forwards;
  animation-delay: calc(var(--item-delay, 0s) + 0.1s);
  opacity: 0;
  transform: scale(0.7) translateY(8px);
  display: inline-block;
  transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);

  &:hover {
    color: #32fefc;
    text-shadow: 0 0 8px rgba(50, 254, 252, 0.6);
    transform: scale(1.05) translateY(-1px);
  }
}

@keyframes numberGlow {
  0% {
    opacity: 0;
    transform: scale(0.7) translateY(8px);
    color: #666;
  }
  30% {
    opacity: 0.6;
    transform: scale(1.1) translateY(2px);
    color: #32fefc;
    text-shadow: 0 0 12px rgba(50, 254, 252, 0.6);
  }
  70% {
    opacity: 0.9;
    transform: scale(0.98) translateY(-1px);
    color: #32fefc;
    text-shadow: 0 0 8px rgba(50, 254, 252, 0.4);
  }
  100% {
    opacity: 1;
    transform: scale(1) translateY(0);
    color: white;
    text-shadow: 0 0 4px rgba(50, 254, 252, 0.2);
  }
}

// 增强的名称动画 - 优化为更平滑的滑入效果
.enhanced-name {
  animation: nameSlideIn 700ms cubic-bezier(0.25, 0.46, 0.45, 0.94) forwards;
  animation-delay: calc(var(--item-delay, 0s) + 0.15s);
  opacity: 0;
  transform: translateX(-25px) scale(0.9);
  transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);

  &:hover {
    color: #32fefc;
    text-shadow: 0 0 6px rgba(50, 254, 252, 0.4);
    transform: translateX(2px) scale(1.02);
  }
}

@keyframes nameSlideIn {
  0% {
    opacity: 0;
    transform: translateX(-25px) scale(0.9);
  }
  50% {
    opacity: 0.7;
    transform: translateX(2px) scale(1.01);
  }
  100% {
    opacity: 1;
    transform: translateX(0) scale(1);
  }
}

// 增强的进度条容器动画 - 简化版本，只保留进入效果
.enhanced-progress-container {
  animation: progressContainerSlideIn 800ms cubic-bezier(0.25, 0.46, 0.45, 0.94) forwards;
  animation-delay: calc(var(--item-delay, 0s) + 0.2s);
  opacity: 0;
  transform: translateX(-20px) scale(0.9);
}

@keyframes progressContainerSlideIn {
  0% {
    opacity: 0;
    transform: translateX(-20px) scale(0.9);
  }
  100% {
    opacity: 1;
    transform: translateX(0) scale(1);
  }
}

// 增强的数值动画 - 优化为更平滑的计数效果
.enhanced-value {
  animation: valueCountUp 900ms cubic-bezier(0.25, 0.46, 0.45, 0.94) forwards;
  animation-delay: calc(var(--item-delay, 0s) + 0.3s);
  opacity: 0;
  transform: scale(0.6) translateY(10px);
  transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);

  &:hover {
    color: #32fefc;
    text-shadow: 0 0 8px rgba(50, 254, 252, 0.6);
    transform: scale(1.05) translateY(-2px);
  }
}

@keyframes valueCountUp {
  0% {
    opacity: 0;
    transform: scale(0.6) translateY(10px);
  }
  25% {
    opacity: 0.5;
    transform: scale(1.15) translateY(3px);
    color: #32fefc;
    text-shadow: 0 0 15px rgba(50, 254, 252, 0.7);
  }
  60% {
    opacity: 0.85;
    transform: scale(0.95) translateY(-1px);
    color: #32fefc;
    text-shadow: 0 0 10px rgba(50, 254, 252, 0.5);
  }
  100% {
    opacity: 1;
    transform: scale(1) translateY(0);
    color: #ffffff;
  }
}

// 数值变化动画 - 优化为更平滑的更新
.progress-fill {
  transition: width 0.6s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  will-change: width;
}

.progress-indicator {
  transition: left 0.6s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  will-change: left;
  animation: indicatorPulse 2s ease-in-out infinite;
}

@keyframes indicatorPulse {
  0%,
  100% {
    transform: translate(-50%, -50%) scale(1);
    filter: brightness(1);
  }
  50% {
    transform: translate(-50%, -50%) scale(1.1);
    filter: brightness(1.2) drop-shadow(0 0 8px rgba(50, 254, 252, 0.6));
  }
}

.progress-value {
  transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  font-variant-numeric: tabular-nums; // 数字等宽，避免跳动
  will-change: transform, color;
}

// 数据更新时的微妙动画效果
.data-updating {
  animation: dataUpdatePulse 0.8s cubic-bezier(0.25, 0.46, 0.45, 0.94) forwards;
}

@keyframes dataUpdatePulse {
  0% {
    transform: scale(1);
    filter: brightness(1);
  }
  50% {
    transform: scale(1.01);
    filter: brightness(1.1) drop-shadow(0 0 10px rgba(50, 254, 252, 0.3));
  }
  100% {
    transform: scale(1);
    filter: brightness(1);
  }
}

// 增强的无数据状态动画
.enhanced-no-data {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 200px;
  color: #666;
  font-size: 14px;
  animation: noDataFadeIn 800ms cubic-bezier(0.25, 0.46, 0.45, 0.94) forwards;
  opacity: 0;
  transform: translateY(20px);

  .no-data-text {
    color: #999;
    font-size: 16px;
    font-weight: 400;
    animation: textPulse 2s ease-in-out infinite;
  }
}

@keyframes noDataFadeIn {
  0% {
    opacity: 0;
    transform: translateY(20px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes textPulse {
  0%,
  100% {
    opacity: 0.6;
  }
  50% {
    opacity: 1;
    color: #32fefc;
    text-shadow: 0 0 10px rgba(50, 254, 252, 0.3);
  }
}

// 暂无数据样式 - 保持原有样式作为备用
.no-data {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 200px;
  color: #666;
  font-size: 14px;
}

.no-data-icon {
  font-size: 48px;
  margin-bottom: 16px;
  opacity: 0.6;
}

.no-data-text {
  color: #999;
  font-size: 16px;
  font-weight: 400;
}
</style>
