import * as THREE from "three";

/**
 * 星空背景系统
 *
 * 创建一个包含数千颗星星的球形星空背景，营造太空环境的氛围。
 *
 * 特性：
 * 1. 随机分布的星星点
 * 2. 不同大小和亮度的星星
 * 3. 微妙的闪烁动画效果
 * 4. 可配置的星星数量和分布
 * 5. 性能优化的粒子系统
 *
 * @class StarField
 */
class StarField {
  constructor(scene, options = {}) {
    this.scene = scene;

    // 配置参数
    this.config = {
      starCount: options.starCount || 8000, // 星星数量
      radius: options.radius || 500, // 星空球体半径
      minSize: options.minSize || 0.5, // 最小星星大小
      maxSize: options.maxSize || 2.0, // 最大星星大小
      minOpacity: options.minOpacity || 0.3, // 最小透明度
      maxOpacity: options.maxOpacity || 1.0, // 最大透明度
      twinkleSpeed: options.twinkleSpeed || 0.002, // 闪烁速度
      enableTwinkle: true, // 是否启用闪烁
      color: options.color || 0xffffff, // 星星颜色
    };

    // Three.js 对象
    this.geometry = null;
    this.material = null;
    this.points = null;

    // 动画相关
    this.time = 0;
    this.originalOpacities = [];
    this.twinkleOffsets = [];

    this.init();
  }

  init() {
    this.createGeometry();
    this.createMaterial();
    this.createPoints();
    this.addToScene();
  }

  /**
   * 创建星星的几何体
   */
  createGeometry() {
    this.geometry = new THREE.BufferGeometry();

    const positions = new Float32Array(this.config.starCount * 3);
    const sizes = new Float32Array(this.config.starCount);
    const opacities = new Float32Array(this.config.starCount);

    // 生成随机分布的星星位置
    for (let i = 0; i < this.config.starCount; i++) {
      const i3 = i * 3;

      // 使用球坐标系统生成均匀分布的点
      const theta = Math.random() * Math.PI * 2; // 方位角 0-2π
      const phi = Math.acos(2 * Math.random() - 1); // 极角，确保均匀分布
      const radius = this.config.radius + (Math.random() - 0.5) * 50; // 添加一些深度变化

      // 转换为笛卡尔坐标
      positions[i3] = radius * Math.sin(phi) * Math.cos(theta);
      positions[i3 + 1] = radius * Math.sin(phi) * Math.sin(theta);
      positions[i3 + 2] = radius * Math.cos(phi);

      // 随机大小
      sizes[i] = this.config.minSize + Math.random() * (this.config.maxSize - this.config.minSize);

      // 随机透明度
      const opacity = this.config.minOpacity + Math.random() * (this.config.maxOpacity - this.config.minOpacity);
      opacities[i] = opacity;

      // 保存原始透明度用于闪烁动画
      this.originalOpacities[i] = opacity;

      // 为每颗星星生成随机的闪烁偏移
      this.twinkleOffsets[i] = Math.random() * Math.PI * 2;
    }

    this.geometry.setAttribute("position", new THREE.BufferAttribute(positions, 3));
    this.geometry.setAttribute("size", new THREE.BufferAttribute(sizes, 1));
    this.geometry.setAttribute("opacity", new THREE.BufferAttribute(opacities, 1));
  }

  /**
   * 创建星星的材质
   */
  createMaterial() {
    // 创建星星纹理
    const canvas = document.createElement("canvas");
    canvas.width = 32;
    canvas.height = 32;
    const context = canvas.getContext("2d");

    // 绘制圆形星星
    const gradient = context.createRadialGradient(16, 16, 0, 16, 16, 16);
    gradient.addColorStop(0, "rgba(255, 255, 255, 1)");
    gradient.addColorStop(0.2, "rgba(255, 255, 255, 0.8)");
    gradient.addColorStop(0.4, "rgba(255, 255, 255, 0.4)");
    gradient.addColorStop(1, "rgba(255, 255, 255, 0)");

    context.fillStyle = gradient;
    context.fillRect(0, 0, 32, 32);

    const texture = new THREE.CanvasTexture(canvas);

    // 创建点材质
    this.material = new THREE.PointsMaterial({
      map: texture,
      color: this.config.color,
      transparent: true,
      opacity: 1,
      vertexColors: false,
      sizeAttenuation: true,
      alphaTest: 0.001,
      blending: THREE.AdditiveBlending, // 使用加法混合模式增强发光效果
    });

    // 如果支持，使用自定义着色器增强效果
    if (this.config.enableTwinkle) {
      this.createCustomMaterial();
    }
  }

  /**
   * 创建自定义着色器材质以支持闪烁效果
   */
  createCustomMaterial() {
    const vertexShader = `
      attribute float size;
      attribute float opacity;
      varying float vOpacity;
      
      void main() {
        vOpacity = opacity;
        vec4 mvPosition = modelViewMatrix * vec4(position, 1.0);
        gl_PointSize = size * (300.0 / -mvPosition.z);
        gl_Position = projectionMatrix * mvPosition;
      }
    `;

    const fragmentShader = `
      uniform sampler2D map;
      uniform vec3 color;
      varying float vOpacity;
      
      void main() {
        vec4 texColor = texture2D(map, gl_PointCoord);
        gl_FragColor = vec4(color, texColor.a * vOpacity);
      }
    `;

    this.material = new THREE.ShaderMaterial({
      uniforms: {
        map: { value: this.material.map },
        color: { value: new THREE.Color(this.config.color) },
      },
      vertexShader,
      fragmentShader,
      transparent: true,
      blending: THREE.AdditiveBlending,
      depthWrite: false,
    });
  }

  /**
   * 创建点系统
   */
  createPoints() {
    this.points = new THREE.Points(this.geometry, this.material);
    this.points.name = "StarField";
  }

  /**
   * 添加到场景
   */
  addToScene() {
    if (this.scene && this.points) {
      this.scene.add(this.points);
    }
  }

  /**
   * 更新动画
   */
  update() {
    if (!this.config.enableTwinkle || !this.geometry) return;

    this.time += this.config.twinkleSpeed;

    const opacities = this.geometry.attributes.opacity.array;

    // 更新每颗星星的闪烁效果
    for (let i = 0; i < this.config.starCount; i++) {
      const twinkle = Math.sin(this.time + this.twinkleOffsets[i]) * 0.3 + 0.7;
      opacities[i] = this.originalOpacities[i] * twinkle;
    }

    this.geometry.attributes.opacity.needsUpdate = true;
  }

  /**
   * 设置星空可见性
   */
  setVisible(visible) {
    if (this.points) {
      this.points.visible = visible;
    }
  }

  /**
   * 更新星星颜色
   */
  setColor(color) {
    this.config.color = color;
    if (this.material) {
      if (this.material.color) {
        this.material.color.set(color);
      } else if (this.material.uniforms && this.material.uniforms.color) {
        this.material.uniforms.color.value.set(color);
      }
    }
  }

  /**
   * 销毁资源
   */
  destroy() {
    if (this.points && this.scene) {
      this.scene.remove(this.points);
    }

    if (this.geometry) {
      this.geometry.dispose();
    }

    if (this.material) {
      if (this.material.map) {
        this.material.map.dispose();
      }
      this.material.dispose();
    }

    this.points = null;
    this.geometry = null;
    this.material = null;
    this.originalOpacities = [];
    this.twinkleOffsets = [];
  }
}

export { StarField };
