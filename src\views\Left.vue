<template>
  <div class="Left" :style="animationStyles">
    <transition name="slide-left">
      <div v-if="uiAnimationTriggered" class="LeftHead1" :style="{ animationDelay: animationDelays[0] }">
        <div class="LeftHead1Text">网络攻击分析</div>
      </div>
    </transition>
    <transition name="slide-left">
      <div v-if="uiAnimationTriggered" class="LeftHead1Body1 scale-animation" :style="{ animationDelay: animationDelays[1] }">
        <div class="LeftHead1Body1_1 enhanced-3d-flip enhanced-3d-flip-1">
          <Label1 :label1Right2Text="debouncedData.countAttackNum || dataStore.data?.intercept?.countAttackNum" :dataUpdateAnimating="dataUpdateAnimating.countAttackNum" />
        </div>
        <div class="LeftHead1Body1_1 LeftHead1Body1_2 enhanced-3d-flip enhanced-3d-flip-2">
          <Label1
            :iconId="2"
            label1Right1Text="自动拦截攻击"
            :label1Right2Text="debouncedData.autIntercept || dataStore.data?.intercept?.autIntercept"
            :dataUpdateAnimating="dataUpdateAnimating.autIntercept"
          />
        </div>
      </div>
    </transition>
    <transition name="slide-left">
      <div v-if="uiAnimationTriggered" class="LeftHead1Body1 LeftHead1Body12 scale-animation scale-animation-2" :style="{ animationDelay: animationDelays[2] }">
        <div class="LeftHead1Body1_1 enhanced-3d-flip enhanced-3d-flip-3">
          <Label1
            :iconId="3"
            label1Right1Text="自动拦截率"
            :label1Right2Text="debouncedData.autoRatio || dataStore.data?.intercept?.autoRatio"
            :dataUpdateAnimating="dataUpdateAnimating.autoRatio"
          />
        </div>
        <div class="LeftHead1Body1_1 LeftHead1Body1_2 enhanced-3d-flip enhanced-3d-flip-4">
          <Label1 :iconId="4" label1Right1Text="告警处置率" label1Right2Text="100%" />
        </div>
      </div>
    </transition>

    <transition name="slide-left">
      <div v-if="uiAnimationTriggered" class="LeftHead1 LeftHead2" :style="{ animationDelay: animationDelays[3] }">
        <div class="LeftHead1Text">攻击风险统计</div>
      </div>
    </transition>
    <!-- <div class="LeftHead2Body"></div> -->
    <div v-if="uiAnimationTriggered" class="LeftHead2Body" :style="{ animationDelay: animationDelays[4] }">
      <Bt1 :enableLoadingAnimation="true" :animationDelay="animationDelays[4]" />
    </div>

    <transition name="slide-left">
      <div v-if="uiAnimationTriggered" class="LeftHead1 LeftHead3" :style="{ animationDelay: animationDelays[5] }">
        <div class="LeftHead1Text">主要攻击来源</div>
      </div>
    </transition>
    <transition name="slide-left">
      <div v-if="uiAnimationTriggered" class="LeftHead3Body" :style="{ animationDelay: animationDelays[6] }">
        <LeftList1 :parentAnimationDelay="animationDelays[6]" />
      </div>
    </transition>

    <transition name="slide-left">
      <div v-if="uiAnimationTriggered" class="LeftHead1 LeftHead4" :style="{ animationDelay: animationDelays[7] }">
        <div class="LeftHead1Text">政击告警风险趋势</div>
      </div>
    </transition>
    <transition name="slide-left">
      <div v-if="uiAnimationTriggered" class="LeftHead4Body" :style="{ animationDelay: animationDelays[8] }">
        <Qx1 :enableLoadingAnimation="true" :animationDelay="animationDelays[8]" />
      </div>
    </transition>
  </div>
</template>

<script setup>
import { ref, computed, inject, watch } from "vue";
import Label1 from "./Label1.vue";
import Bt1 from "./Bt1.vue";
import LeftList1 from "./LeftList1.vue";
import Qx1 from "./Qx1.vue";

import { usedata } from "../store/data";
const dataStore = usedata();

// 防抖数据，减少频繁更新导致的抖动
const debouncedData = ref({
  countAttackNum: null,
  autIntercept: null,
  autoRatio: null,
});

// 防抖定时器
let debounceTimer = null;

// 数据更新动画状态
const dataUpdateAnimating = ref({
  countAttackNum: false,
  autIntercept: false,
  autoRatio: false,
});

// 触发数据更新动画
const triggerDataUpdateAnimation = (field) => {
  dataUpdateAnimating.value[field] = true;
  setTimeout(() => {
    dataUpdateAnimating.value[field] = false;
  }, 600); // 动画持续时间
};

// 监听数据变化并进行防抖处理
watch(
  () => dataStore.data?.intercept,
  (newIntercept) => {
    if (debounceTimer) {
      clearTimeout(debounceTimer);
    }

    debounceTimer = setTimeout(() => {
      if (newIntercept) {
        const oldData = debouncedData.value;
        const newData = {
          countAttackNum: newIntercept.countAttackNum,
          autIntercept: newIntercept.autIntercept,
          autoRatio: newIntercept.autoRatio,
        };

        // 检查哪些字段发生了变化，触发相应的动画
        Object.keys(newData).forEach((key) => {
          if (oldData[key] !== newData[key] && oldData[key] !== null) {
            triggerDataUpdateAnimation(key);
          }
        });

        debouncedData.value = newData;
      }
    }, 150); // 150ms 防抖延迟，平衡响应性和稳定性
  },
  { deep: true, immediate: true }
);

// 注入UI动画触发状态
const uiAnimationTriggered = inject("uiAnimationTriggered", ref(false));

// 动画总时长配置 (秒)
const totalAnimationDuration = ref(1.5);

// 计算单个动画持续时间 (总时长的1/3)
const singleAnimationDuration = computed(() => {
  return totalAnimationDuration.value / 3;
});

// 计算动画延迟间隔 (总时长除以元素数量)
const delayInterval = computed(() => {
  return totalAnimationDuration.value / 9; // 9个元素
});

// 计算各个元素的动画延迟
const animationDelays = computed(() => {
  const delays = [];
  for (let i = 0; i < 9; i++) {
    delays.push((i * delayInterval.value).toFixed(2) + "s");
  }
  return delays;
});

// CSS 变量，用于动态设置动画时长
const animationStyles = computed(() => {
  return {
    "--animation-duration": singleAnimationDuration.value + "s",
    "--total-duration": totalAnimationDuration.value + "s",
  };
});
</script>

<style lang="less" scoped>
/* 滑动动画样式 */
.slide-left-enter-active {
  // animation: slideInFromLeft var(--animation-duration, 1s) ease-out forwards;
  //回弹
  animation: slideInFromLeft var(--animation-duration, 1s) ease-out forwards;
}

.slide-left-enter-from {
  transform: translateX(-100%);
  opacity: 0;
}

@keyframes slideInFromLeft {
  0% {
    transform: translateX(-100%);
    opacity: 0;
  }
  100% {
    transform: translateX(0);
    opacity: 1;
  }
}

/* 为每个元素添加初始状态和动画延迟 */
.LeftHead1,
.LeftHead1Body1,
.LeftHead1Body12,
.LeftHead2,
.LeftHead3,
.LeftHead3Body,
.LeftHead4,
.LeftHead4Body {
  animation: slideInFromLeft var(--animation-duration, 1s) ease-out forwards;
  transform: translateX(-100%);
  opacity: 0;
}

/* LeftHead2Body 不使用滑动动画，直接显示 */
.LeftHead2Body {
  opacity: 1;
  transform: translateX(0);
}

/* 增强3D翻转动画效果 - 优化为更丝滑的动画 */
.enhanced-3d-flip {
  perspective: 1200px;
  transform-style: preserve-3d;
  animation: enhanced3DFlip 800ms cubic-bezier(0.25, 0.46, 0.45, 0.94) forwards;
  transform: rotateY(-90deg) scale(0.8);
  opacity: 0;
  filter: drop-shadow(0 10px 20px rgba(50, 254, 252, 0.3));
  transition: all 0.6s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  will-change: transform, opacity, filter;
  backface-visibility: hidden;
}

.enhanced-3d-flip:hover {
  transform: rotateY(0deg) scale(1.05);
  filter: drop-shadow(0 15px 30px rgba(50, 254, 252, 0.5)) brightness(1.1);
}

@keyframes enhanced3DFlip {
  0% {
    transform: rotateY(-90deg) scale(0.8);
    opacity: 0;
    filter: drop-shadow(0 5px 10px rgba(50, 254, 252, 0.2));
  }
  25% {
    transform: rotateY(-60deg) scale(0.85);
    opacity: 0.3;
    filter: drop-shadow(0 6px 12px rgba(50, 254, 252, 0.25));
  }
  50% {
    transform: rotateY(-30deg) scale(0.92);
    opacity: 0.6;
    filter: drop-shadow(0 8px 16px rgba(50, 254, 252, 0.35));
  }
  75% {
    transform: rotateY(-10deg) scale(0.98);
    opacity: 0.85;
    filter: drop-shadow(0 9px 18px rgba(50, 254, 252, 0.4));
  }
  100% {
    transform: rotateY(0deg) scale(1);
    opacity: 1;
    filter: drop-shadow(0 10px 20px rgba(50, 254, 252, 0.3));
  }
}

/* 数据更新时的平滑过渡动画 */
.enhanced-3d-flip .Label1Right2 {
  transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  will-change: transform, opacity;
}

/* 缩放动画效果 - 从小到大 */
.scale-animation {
  animation: scaleUpAnimation 1200ms cubic-bezier(0.175, 0.885, 0.32, 1.275) forwards;
  animation-delay: calc(var(--animation-duration, 0.33s) + 0.1s); /* 在滑动动画完成后0.1秒开始 */
  transform: scale(0.3);
  opacity: 1; /* 滑动动画已经处理了opacity */
  transform-origin: center center;
}

.scale-animation-2 {
  animation-delay: calc(var(--animation-duration, 0.33s) + 0.2s); /* 第二个容器延迟更久 */
}

/* 3D翻转动画的不同延迟 - 优化为更丝滑的渐进式动画 */
.enhanced-3d-flip-1 {
  animation-delay: calc(var(--animation-duration, 0.33s) + 0.15s);
}

.enhanced-3d-flip-2 {
  animation-delay: calc(var(--animation-duration, 0.33s) + 0.25s);
}

.enhanced-3d-flip-3 {
  animation-delay: calc(var(--animation-duration, 0.33s) + 0.35s);
}

.enhanced-3d-flip-4 {
  animation-delay: calc(var(--animation-duration, 0.33s) + 0.45s);
}

@keyframes scaleUpAnimation {
  0% {
    transform: scale(0.2);
    filter: blur(3px) brightness(0.7);
  }
  20% {
    transform: scale(0.4);
    filter: blur(2px) brightness(0.8);
  }
  50% {
    transform: scale(0.8);
    filter: blur(1px) brightness(0.95);
  }
  80% {
    transform: scale(1.1);
    filter: blur(0px) brightness(1.15);
  }
  100% {
    transform: scale(1);
    filter: blur(0px) brightness(1);
  }
}

.Left {
  width: 100%;
  height: 100%;
  position: relative;
  .LeftHead1 {
    position: absolute;
    left: 147px;
    top: 15px;
    width: 380px;
    height: 35px;
    background-image: url("../../public/img/cardHead.png");
    background-repeat: no-repeat;
    background-size: 100% 100%;
    .LeftHead1Text {
      font-family: AlimamaShuHeiTi, AlimamaShuHeiTi;
      font-weight: bold;
      font-size: 18px;
      color: #ffffff;
      text-align: left;
      font-style: normal;
      margin-left: 25px;
    }
  }
  .LeftHead2 {
    position: absolute;
    left: 91.0696px;
    top: 200px;
  }
  .LeftHead3 {
    position: absolute;
    left: 77px;
    top: 423px;
  }
  .LeftHead4 {
    position: absolute;
    left: 92.25px;
    top: 659px;
  }
  .LeftHead1Body1 {
    position: absolute;
    left: 125px;
    top: 71px;
    width: 365px;
    height: 50px;

    display: flex;
    justify-content: center;
    align-items: center;

    .LeftHead1Body1_1 {
      width: 50%;
      height: 100%;
    }
    .LeftHead1Body1_2 {
      width: 50%;
      height: 100%;
    }
  }
  .LeftHead1Body12 {
    position: absolute;
    left: 106px;
    top: 130px;
  }
  .LeftHead2Body {
    position: absolute;
    left: 89.7361px;
    top: 253px;
    width: 370px;
    height: 150px;
  }
  .LeftHead3Body {
    position: absolute;
    left: 85px;
    top: 455px;
    width: 370px;
    height: 170px;
  }
  .LeftHead4Body {
    position: absolute;
    left: 102px;
    top: 694px;
    width: 370px;
    height: 170px;
  }
}
</style>
